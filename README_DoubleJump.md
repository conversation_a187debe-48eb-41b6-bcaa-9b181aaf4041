# Super Mario 二段跳功能实现

## 功能说明
已成功为Super Mario游戏添加了二段跳功能，允许玩家在空中进行第二次跳跃。

## 实现的修改

### 1. <PERSON>类新增属性 (mario.h)
```cpp
int jump_count;        // 跳跃次数计数器（用于二段跳）
bool can_double_jump;  // 是否可以进行二段跳
```

### 2. 初始化代码 (mario.cpp)
在Mario::Init函数中初始化新属性：
```cpp
gamescene->mario->jump_count = 0;
gamescene->mario->can_double_jump = false;
```

### 3. 新增二段跳事件函数 (event.cpp)
```cpp
void MarioDoubleJumpEvent(Mario* mario) {
    mario->is_jump = true;
    mario->is_land = false;
    mario->jump_count = 2;
    mario->can_double_jump = false;
    mario->vy = -32 * sqrt(2) * 0.04 * 4;  // 二段跳力度稍小
    playAudio("big_jump");
}
```

### 4. 修改跳跃输入处理 (gamescene.cpp)
添加了按键触发器机制，支持：
- 第一次跳跃：在地面上按K键
- 二段跳：在空中时再次按K键

### 5. 着地重置逻辑 (check.cpp)
在碰撞检测函数中添加着地时重置跳跃计数器的逻辑。

## 编译说明

### 方法1：使用Visual Studio
1. 双击打开 `SuperMario.sln`
2. 选择 Debug x64 配置
3. 按 F5 或点击"开始调试"

### 方法2：使用Visual Studio Code
1. 安装C/C++扩展
2. 打开项目文件夹
3. 使用Ctrl+Shift+P，选择"Tasks: Run Build Task"

### 方法3：命令行编译
如果安装了Visual Studio Build Tools：
```cmd
"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" SuperMario\SuperMario.vcxproj /p:Configuration=Debug /p:Platform=x64
```

## 游戏控制

- **A/D** - 左右移动
- **K** - 跳跃（支持二段跳）
- **J** - 加速/发射火球
- **S** - 蹲下

## 二段跳使用方法

1. 按 **K** 键进行第一次跳跃
2. 在空中时再次按 **K** 键进行二段跳
3. 着地后可以重新进行完整的跳跃序列

## 故障排除

如果遇到编译错误：
1. 确保安装了Visual Studio 2019或更高版本
2. 确保安装了EasyX图形库
3. 检查项目配置是否为Debug x64
4. 清理并重新生成解决方案

如果游戏无法运行：
1. 确保安装了Visual C++ Redistributable
2. 检查resources文件夹是否存在
3. 尝试以管理员权限运行

## 技术细节

- 二段跳力度比第一次跳跃稍小，保持游戏平衡
- 使用触发器机制避免按键连续触发
- 着地时自动重置跳跃状态，允许重新进行二段跳
- 保持了原有游戏机制的完整性
