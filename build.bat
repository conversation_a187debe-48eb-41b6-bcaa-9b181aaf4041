@echo off
echo Building SuperMario with double jump feature...

REM Try to find MSBuild
set MSBUILD_PATH=""

REM Check common Visual Studio 2022 locations
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
)
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
)
if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
)

REM Check Visual Studio 2019 locations
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
)

if %MSBUILD_PATH%=="" (
    echo MSBuild not found. Please install Visual Studio or Build Tools.
    pause
    exit /b 1
)

echo Using MSBuild at: %MSBUILD_PATH%

REM Clean previous build
echo Cleaning previous build...
if exist "x64\Debug\*.obj" del "x64\Debug\*.obj"
if exist "SuperMario\x64\Debug\*.obj" del "SuperMario\x64\Debug\*.obj"

REM Build the project
echo Building project...
%MSBUILD_PATH% SuperMario\SuperMario.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:ForceRebuild=true

if %ERRORLEVEL%==0 (
    echo Build successful!
    echo Executable location: x64\Debug\SuperMario.exe
    echo.
    echo Controls:
    echo A/D - Move left/right
    echo K - Jump (supports double jump now!)
    echo J - Speed/Fire
    echo S - Crouch
    echo.
    echo Starting game...
    start "" "x64\Debug\SuperMario.exe"
) else (
    echo Build failed with error code %ERRORLEVEL%
    pause
)
