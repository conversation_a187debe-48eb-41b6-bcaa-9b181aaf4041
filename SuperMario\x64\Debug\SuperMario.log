﻿  camera.cpp
D:\SuperMario-master\SuperMario\mario.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“camera.cpp”)
  
D:\SuperMario-master\SuperMario\camera.cpp(18,22): error C2248: “Mario::is_flag”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(20,7):
      参见“Mario::is_flag”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\camera.cpp(18,59): error C2248: “Mario::is_enter”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(19,7):
      参见“Mario::is_enter”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
  check.cpp
D:\SuperMario-master\SuperMario\check.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\SuperMario-master\SuperMario\mario.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“check.cpp”)
  
D:\SuperMario-master\SuperMario\check.cpp(103,66): error C2065: “i”: 未声明的标识符
D:\SuperMario-master\SuperMario\check.cpp(105,4): error C2044: 非法 continue
D:\SuperMario-master\SuperMario\check.cpp(176,13): error C2248: “Mario::jump_count”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(22,6):
      参见“Mario::jump_count”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\check.cpp(177,13): error C2248: “Mario::can_double_jump”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(23,7):
      参见“Mario::can_double_jump”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\check.cpp(178,15): error C2039: "is_jump": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\check.cpp(184,1): error C2059: 语法错误:“}”
D:\SuperMario-master\SuperMario\check.cpp(184,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
D:\SuperMario-master\SuperMario\check.cpp(186,40): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\SuperMario-master\SuperMario\check.cpp(186,40): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\SuperMario-master\SuperMario\check.cpp(687,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\SuperMario-master\SuperMario\check.cpp(706,39): error C2065: “pBlock”: 未声明的标识符
D:\SuperMario-master\SuperMario\check.cpp(706,66): error C2065: “pBlock”: 未声明的标识符
D:\SuperMario-master\SuperMario\check.cpp(708,53): error C2065: “pBlock”: 未声明的标识符
D:\SuperMario-master\SuperMario\check.cpp(708,97): error C2065: “pBlock”: 未声明的标识符
D:\SuperMario-master\SuperMario\check.cpp(710,19): error C2065: “pBlock”: 未声明的标识符
D:\SuperMario-master\SuperMario\check.cpp(713,36): error C2065: “pBlock”: 未声明的标识符
D:\SuperMario-master\SuperMario\check.cpp(713,63): error C2065: “pBlock”: 未声明的标识符
D:\SuperMario-master\SuperMario\check.cpp(715,19): error C2065: “pBlock”: 未声明的标识符
D:\SuperMario-master\SuperMario\check.cpp(720,38): error C2065: “pBlock”: 未声明的标识符
D:\SuperMario-master\SuperMario\check.cpp(720,69): error C2065: “pBlock”: 未声明的标识符
D:\SuperMario-master\SuperMario\check.cpp(720,133): error C2065: “pBlock”: 未声明的标识符
D:\SuperMario-master\SuperMario\check.cpp(720,178): error C2065: “pBlock”: 未声明的标识符
D:\SuperMario-master\SuperMario\check.cpp(725,18): error C2065: “pBlock”: 未声明的标识符
D:\SuperMario-master\SuperMario\check.cpp(732,3): error C2065: “pEntity”: 未声明的标识符
D:\SuperMario-master\SuperMario\check.cpp(732,22): error C2065: “island”: 未声明的标识符
D:\SuperMario-master\SuperMario\check.cpp(734,1): error C2059: 语法错误:“}”
D:\SuperMario-master\SuperMario\check.cpp(734,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
D:\SuperMario-master\SuperMario\check.cpp(736,39): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\SuperMario-master\SuperMario\check.cpp(736,39): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\SuperMario-master\SuperMario\check.cpp(782,24): error C2248: “Mario::is_death”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(18,7):
      参见“Mario::is_death”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\check.cpp(782,62): error C2248: “Mario::is_enter”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(19,7):
      参见“Mario::is_enter”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\check.cpp(783,3): error C3861: “MarioTouchBlockCheck”: 找不到标识符
D:\SuperMario-master\SuperMario\check.cpp(785,3): error C3861: “MarioTouchWallCheck”: 找不到标识符
D:\SuperMario-master\SuperMario\check.cpp(790,3): error C3861: “MarioTouchPropCheck”: 找不到标识符
D:\SuperMario-master\SuperMario\check.cpp(793,3): error C3861: “MarioTouchMonsterCheck”: 找不到标识符
D:\SuperMario-master\SuperMario\check.cpp(795,3): error C3861: “MarioTouchFlagCheck”: 找不到标识符
D:\SuperMario-master\SuperMario\check.cpp(798,2): error C3861: “MarioEnterPipeCheck”: 找不到标识符
D:\SuperMario-master\SuperMario\check.cpp(803,2): error C3861: “PropTouchWallCheck”: 找不到标识符
D:\SuperMario-master\SuperMario\check.cpp(808,2): error C3861: “FireballTouchBlockCheck”: 找不到标识符
D:\SuperMario-master\SuperMario\check.cpp(810,2): error C3861: “FireballTouchWallCheck”: 找不到标识符
D:\SuperMario-master\SuperMario\check.cpp(815,2): error C3861: “MonsterTouchBlockCheck”: 找不到标识符
D:\SuperMario-master\SuperMario\check.cpp(817,2): error C3861: “MonsterTouchWallCheck”: 找不到标识符
D:\SuperMario-master\SuperMario\check.cpp(822,2): error C3861: “MonsterTouchMonsterCheck”: 找不到标识符
D:\SuperMario-master\SuperMario\check.cpp(824,2): error C3861: “FireballTouchMonsterCheck”: 找不到标识符
D:\SuperMario-master\SuperMario\check.cpp(826,1): error C1004: 发现意外的文件尾
  event.cpp
D:\SuperMario-master\SuperMario\event.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\SuperMario-master\SuperMario\mario.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“event.cpp”)
  
D:\SuperMario-master\SuperMario\event.cpp(22,22): error C2248: “Mario::in_time”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(25,6):
      参见“Mario::in_time”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(27,18): error C2248: “Mario::is_flag”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(20,7):
      参见“Mario::is_flag”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(29,20): error C2039: "is_squat": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(30,18): error C2248: “Mario::is_run”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(16,7):
      参见“Mario::is_run”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(31,20): error C2039: "is_jump": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(32,18): error C2248: “Mario::im_time”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(26,6):
      参见“Mario::im_time”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(33,18): error C2248: “Mario::in_time”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(25,6):
      参见“Mario::in_time”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(34,18): error C2248: “Mario::mario_dir”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(24,6):
      参见“Mario::mario_dir”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(78,19): error C2248: “Mario::is_run”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(16,7):
      参见“Mario::is_run”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(80,19): error C2248: “Mario::is_run”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(16,7):
      参见“Mario::is_run”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(81,18): error C2248: “Mario::is_enter”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(19,7):
      参见“Mario::is_enter”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(82,20): error C2039: "is_jump": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(83,18): error C2248: “Mario::is_speed”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(17,7):
      参见“Mario::is_speed”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(84,18): error C2248: “Mario::im_time”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(26,6):
      参见“Mario::im_time”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(85,18): error C2248: “Mario::in_time”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(25,6):
      参见“Mario::in_time”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(131,2): error C3927: "->": 非函数声明符后不允许尾随返回类型
D:\SuperMario-master\SuperMario\event.cpp(131,13): error C3613: “->”后缺少返回类型(假定为“int”)
D:\SuperMario-master\SuperMario\event.cpp(131,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\SuperMario-master\SuperMario\event.cpp(131,13): error C2146: 语法错误: 缺少“;”(在标识符“mario”的前面)
D:\SuperMario-master\SuperMario\event.cpp(132,2): error C3927: "->": 非函数声明符后不允许尾随返回类型
D:\SuperMario-master\SuperMario\event.cpp(132,13): error C3613: “->”后缺少返回类型(假定为“int”)
D:\SuperMario-master\SuperMario\event.cpp(132,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\SuperMario-master\SuperMario\event.cpp(132,2): error C2086: “int gamescene”: 重定义
      D:\SuperMario-master\SuperMario\event.cpp(131,2):
      参见“gamescene”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(132,13): error C2146: 语法错误: 缺少“;”(在标识符“mario”的前面)
D:\SuperMario-master\SuperMario\event.cpp(133,2): error C3927: "->": 非函数声明符后不允许尾随返回类型
D:\SuperMario-master\SuperMario\event.cpp(133,13): error C3613: “->”后缺少返回类型(假定为“int”)
D:\SuperMario-master\SuperMario\event.cpp(133,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\SuperMario-master\SuperMario\event.cpp(133,2): error C2086: “int gamescene”: 重定义
      D:\SuperMario-master\SuperMario\event.cpp(131,2):
      参见“gamescene”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(133,13): error C2146: 语法错误: 缺少“;”(在标识符“mario”的前面)
D:\SuperMario-master\SuperMario\event.cpp(134,2): error C3927: "->": 非函数声明符后不允许尾随返回类型
D:\SuperMario-master\SuperMario\event.cpp(134,13): error C3613: “->”后缺少返回类型(假定为“int”)
D:\SuperMario-master\SuperMario\event.cpp(134,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\SuperMario-master\SuperMario\event.cpp(134,2): error C2086: “int gamescene”: 重定义
      D:\SuperMario-master\SuperMario\event.cpp(131,2):
      参见“gamescene”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(134,13): error C2146: 语法错误: 缺少“;”(在标识符“mario”的前面)
D:\SuperMario-master\SuperMario\event.cpp(135,2): error C3927: "->": 非函数声明符后不允许尾随返回类型
D:\SuperMario-master\SuperMario\event.cpp(135,13): error C3613: “->”后缺少返回类型(假定为“int”)
D:\SuperMario-master\SuperMario\event.cpp(135,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\SuperMario-master\SuperMario\event.cpp(135,2): error C2086: “int gamescene”: 重定义
      D:\SuperMario-master\SuperMario\event.cpp(131,2):
      参见“gamescene”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(135,13): error C2146: 语法错误: 缺少“;”(在标识符“mario”的前面)
D:\SuperMario-master\SuperMario\event.cpp(136,2): error C3927: "->": 非函数声明符后不允许尾随返回类型
D:\SuperMario-master\SuperMario\event.cpp(136,13): error C3613: “->”后缺少返回类型(假定为“int”)
D:\SuperMario-master\SuperMario\event.cpp(136,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\SuperMario-master\SuperMario\event.cpp(136,2): error C2086: “int gamescene”: 重定义
      D:\SuperMario-master\SuperMario\event.cpp(131,2):
      参见“gamescene”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(136,13): error C2146: 语法错误: 缺少“;”(在标识符“mario”的前面)
D:\SuperMario-master\SuperMario\event.cpp(137,2): error C3927: "->": 非函数声明符后不允许尾随返回类型
D:\SuperMario-master\SuperMario\event.cpp(137,13): error C3613: “->”后缺少返回类型(假定为“int”)
D:\SuperMario-master\SuperMario\event.cpp(137,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\SuperMario-master\SuperMario\event.cpp(137,2): error C2086: “int gamescene”: 重定义
      D:\SuperMario-master\SuperMario\event.cpp(131,2):
      参见“gamescene”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(137,13): error C2146: 语法错误: 缺少“;”(在标识符“life”的前面)
D:\SuperMario-master\SuperMario\event.cpp(138,2): error C3927: "->": 非函数声明符后不允许尾随返回类型
D:\SuperMario-master\SuperMario\event.cpp(138,13): error C3613: “->”后缺少返回类型(假定为“int”)
D:\SuperMario-master\SuperMario\event.cpp(138,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\SuperMario-master\SuperMario\event.cpp(138,2): error C2086: “int gamescene”: 重定义
      D:\SuperMario-master\SuperMario\event.cpp(131,2):
      参见“gamescene”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(138,13): error C2146: 语法错误: 缺少“;”(在标识符“body”的前面)
D:\SuperMario-master\SuperMario\event.cpp(139,2): error C2059: 语法错误:“switch”
D:\SuperMario-master\SuperMario\event.cpp(139,34): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\SuperMario-master\SuperMario\event.cpp(139,34): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\SuperMario-master\SuperMario\event.cpp(171,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\SuperMario-master\SuperMario\event.cpp(171,2): error C2365: “stopBGM”: 重定义；以前的定义是“函数”
      D:\SuperMario-master\SuperMario\audio.h(6,6):
      参见“stopBGM”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(172,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\SuperMario-master\SuperMario\event.cpp(172,2): error C2365: “playAudio”: 重定义；以前的定义是“函数”
      D:\SuperMario-master\SuperMario\audio.h(3,6):
      参见“playAudio”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(173,1): error C2059: 语法错误:“}”
D:\SuperMario-master\SuperMario\event.cpp(173,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
D:\SuperMario-master\SuperMario\event.cpp(175,45): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\SuperMario-master\SuperMario\event.cpp(175,45): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\SuperMario-master\SuperMario\event.cpp(239,20): error C2039: "fireCnt": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(246,18): error C2248: “Mario::im_time”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(26,6):
      参见“Mario::im_time”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(260,22): error C2248: “Mario::body”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(28,12):
      参见“Mario::body”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(261,19): error C2248: “Mario::body”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(28,12):
      参见“Mario::body”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(263,19): error C2248: “Mario::transf_time”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(27,6):
      参见“Mario::transf_time”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(264,25): error C2039: "is_squat": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(269,27): error C2248: “Mario::body”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(28,12):
      参见“Mario::body”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(271,19): error C2248: “Mario::body”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(28,12):
      参见“Mario::body”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(272,19): error C2248: “Mario::transf_time”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(27,6):
      参见“Mario::transf_time”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(279,2): error C2046: 非法的 case
D:\SuperMario-master\SuperMario\event.cpp(284,2): error C2046: 非法的 case
D:\SuperMario-master\SuperMario\event.cpp(285,2): error C2046: 非法的 case
D:\SuperMario-master\SuperMario\event.cpp(288,2): error C2046: 非法的 case
D:\SuperMario-master\SuperMario\event.cpp(293,2): error C2046: 非法的 case
D:\SuperMario-master\SuperMario\event.cpp(283,3): error C2043: 非法 break
D:\SuperMario-master\SuperMario\event.cpp(287,3): error C2043: 非法 break
D:\SuperMario-master\SuperMario\event.cpp(292,3): error C2043: 非法 break
D:\SuperMario-master\SuperMario\event.cpp(295,3): error C2043: 非法 break
D:\SuperMario-master\SuperMario\event.cpp(297,1): error C2059: 语法错误:“}”
D:\SuperMario-master\SuperMario\event.cpp(297,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
D:\SuperMario-master\SuperMario\event.cpp(299,58): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\SuperMario-master\SuperMario\event.cpp(299,58): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\SuperMario-master\SuperMario\event.cpp(368,13): error C2248: “Mario::body”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(28,12):
      参见“Mario::body”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(377,4): error C3861: “CreatePropEvent”: 找不到标识符
D:\SuperMario-master\SuperMario\event.cpp(383,3): error C3861: “CreatePropEvent”: 找不到标识符
D:\SuperMario-master\SuperMario\event.cpp(398,11): error C2248: “Mario::body”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(28,12):
      参见“Mario::body”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(406,11): error C2248: “Mario::body”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(28,12):
      参见“Mario::body”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(415,7): error C2248: “Mario::jump_count”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(22,6):
      参见“Mario::jump_count”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(421,9): error C2039: "is_jump": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(423,7): error C2248: “Mario::jump_count”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(22,6):
      参见“Mario::jump_count”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(431,1): error C2059: 语法错误:“}”
D:\SuperMario-master\SuperMario\event.cpp(431,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
D:\SuperMario-master\SuperMario\event.cpp(433,35): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\SuperMario-master\SuperMario\event.cpp(433,35): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\SuperMario-master\SuperMario\event.cpp(467,11): error C2039: "runF": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\event.cpp(470,2): error C2059: 语法错误:“else”
D:\SuperMario-master\SuperMario\event.cpp(470,2): error C1003: 错误计数超过 100；正在停止编译
  fireball.cpp
D:\SuperMario-master\SuperMario\mario.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“fireball.cpp”)
  
D:\SuperMario-master\SuperMario\fireball.cpp(78,22): error C2248: “Mario::mario_dir”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(24,6):
      参见“Mario::mario_dir”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\fireball.cpp(85,38): error C2248: “Mario::mario_dir”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(24,6):
      参见“Mario::mario_dir”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\fireball.cpp(87,35): error C2248: “Mario::mario_dir”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(24,6):
      参见“Mario::mario_dir”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
  flag.cpp
D:\SuperMario-master\SuperMario\mario.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“flag.cpp”)
  
D:\SuperMario-master\SuperMario\flag.cpp(60,22): error C2248: “Mario::is_flag”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(20,7):
      参见“Mario::is_flag”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
  gamescene.cpp
D:\SuperMario-master\SuperMario\gamescene.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\SuperMario-master\SuperMario\mario.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“gamescene.cpp”)
  
D:\SuperMario-master\SuperMario\gamescene.cpp(35,21): error C2248: “Mario::Draw”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(30,7):
      参见“Mario::Draw”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\gamescene.cpp(39,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\SuperMario-master\SuperMario\gamescene.cpp(39,10): error C2065: “gamescene”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(40,2): error C2059: 语法错误:“if”
D:\SuperMario-master\SuperMario\gamescene.cpp(50,22): error C2248: “Mario::is_death”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(18,7):
      参见“Mario::is_death”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\gamescene.cpp(50,60): error C2248: “Mario::is_enter”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(19,7):
      参见“Mario::is_enter”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\gamescene.cpp(50,98): error C2248: “Mario::is_flag”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(20,7):
      参见“Mario::is_flag”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\gamescene.cpp(53,22): error C2065: “msg”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(55,7): error C2065: “msg”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(57,12): error C2065: “msg”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(60,15): error C2065: “w”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(62,15): error C2065: “s”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(64,15): error C2065: “a”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(66,15): error C2065: “d”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(68,15): error C2065: “j”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(70,15): error C2065: “k”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(72,15): error C2065: “t”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(75,7): error C2065: “msg”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(77,12): error C2065: “msg”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(80,15): error C2065: “w”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(82,15): error C2065: “s”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(84,15): error C2065: “a”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(86,15): error C2065: “d”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(88,15): error C2065: “j”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(90,15): error C2065: “k”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(92,15): error C2065: “t”: 未声明的标识符
D:\SuperMario-master\SuperMario\gamescene.cpp(98,19): error C2248: “Mario::mario_dir”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(24,6):
      参见“Mario::mario_dir”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\gamescene.cpp(100,19): error C2248: “Mario::is_run”: 无法访问 private 成员(在“Mario”类中声明)
      D:\SuperMario-master\SuperMario\mario.h(16,7):
      参见“Mario::is_run”的声明
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\gamescene.cpp(103,2): error C2059: 语法错误:“if”
D:\SuperMario-master\SuperMario\gamescene.cpp(103,9): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\SuperMario-master\SuperMario\gamescene.cpp(103,9): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\SuperMario-master\SuperMario\gamescene.cpp(109,2): error C2059: 语法错误:“if”
D:\SuperMario-master\SuperMario\gamescene.cpp(112,2): error C2059: 语法错误:“if”
D:\SuperMario-master\SuperMario\gamescene.cpp(115,2): error C3927: "->": 非函数声明符后不允许尾随返回类型
D:\SuperMario-master\SuperMario\gamescene.cpp(115,13): error C3613: “->”后缺少返回类型(假定为“int”)
D:\SuperMario-master\SuperMario\gamescene.cpp(115,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\SuperMario-master\SuperMario\gamescene.cpp(115,13): error C2146: 语法错误: 缺少“;”(在标识符“mario”的前面)
D:\SuperMario-master\SuperMario\gamescene.cpp(118,3): error C2059: 语法错误:“if”
D:\SuperMario-master\SuperMario\gamescene.cpp(120,2): error C2059: 语法错误:“}”
D:\SuperMario-master\SuperMario\gamescene.cpp(120,2): error C2143: 语法错误: 缺少“;”(在“}”的前面)
D:\SuperMario-master\SuperMario\gamescene.cpp(121,7): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\SuperMario-master\SuperMario\gamescene.cpp(121,7): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\SuperMario-master\SuperMario\gamescene.cpp(125,2): error C2059: 语法错误:“if”
D:\SuperMario-master\SuperMario\gamescene.cpp(125,22): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\SuperMario-master\SuperMario\gamescene.cpp(125,22): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\SuperMario-master\SuperMario\gamescene.cpp(133,2): error C2059: 语法错误:“if”
D:\SuperMario-master\SuperMario\gamescene.cpp(133,68): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\SuperMario-master\SuperMario\gamescene.cpp(133,68): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\SuperMario-master\SuperMario\gamescene.cpp(137,2): error C2059: 语法错误:“if”
D:\SuperMario-master\SuperMario\gamescene.cpp(137,9): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\SuperMario-master\SuperMario\gamescene.cpp(137,9): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\SuperMario-master\SuperMario\gamescene.cpp(141,2): error C2059: 语法错误:“else”
D:\SuperMario-master\SuperMario\gamescene.cpp(141,7): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\SuperMario-master\SuperMario\gamescene.cpp(141,7): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\SuperMario-master\SuperMario\gamescene.cpp(145,2): error C2059: 语法错误:“if”
D:\SuperMario-master\SuperMario\gamescene.cpp(145,22): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\SuperMario-master\SuperMario\gamescene.cpp(145,22): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\SuperMario-master\SuperMario\gamescene.cpp(151,1): error C2059: 语法错误:“}”
D:\SuperMario-master\SuperMario\gamescene.cpp(151,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
D:\SuperMario-master\SuperMario\gamescene.cpp(153,44): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\SuperMario-master\SuperMario\gamescene.cpp(153,44): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\SuperMario-master\SuperMario\gamescene.cpp(360,1): error C1004: 发现意外的文件尾
  mario.cpp
D:\SuperMario-master\SuperMario\mario.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\SuperMario-master\SuperMario\mario.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“mario.cpp”)
  
D:\SuperMario-master\SuperMario\mario.cpp(23,20): error C2039: "is_jump": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(26,20): error C2039: "is_squat": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(31,20): error C2039: "fireCnt": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(35,20): error C2039: "runF": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(36,20): error C2039: "runCnt": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(54,13): error C2039: "runF": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(55,4): error C2065: “F”: 未声明的标识符
D:\SuperMario-master\SuperMario\mario.cpp(57,4): error C2065: “F”: 未声明的标识符
D:\SuperMario-master\SuperMario\mario.cpp(59,37): error C2065: “F”: 未声明的标识符
D:\SuperMario-master\SuperMario\mario.cpp(60,37): error C2065: “F”: 未声明的标识符
D:\SuperMario-master\SuperMario\mario.cpp(61,33): error C2065: “F”: 未声明的标识符
D:\SuperMario-master\SuperMario\mario.cpp(75,15): error C2039: "is_squat": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(85,15): error C2039: "is_squat": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(104,13): error C2039: "is_jump": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(109,25): error C2065: “runF”: 未声明的标识符
D:\SuperMario-master\SuperMario\mario.cpp(112,13): error C2039: "is_squat": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(114,18): error C2039: "is_jump": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(119,25): error C2065: “runF”: 未声明的标识符
D:\SuperMario-master\SuperMario\mario.cpp(122,13): error C2039: "is_squat": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(124,18): error C2039: "fireCnt": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(126,18): error C2039: "is_jump": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(131,23): error C2065: “runF”: 未声明的标识符
D:\SuperMario-master\SuperMario\mario.cpp(135,13): error C2039: "is_squat": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(142,14): error C2039: "is_squat": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(146,14): error C2039: "is_squat": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(230,24): error C2039: "fireCnt": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(231,21): error C2039: "fireCnt": 不是 "Mario" 的成员
      D:\SuperMario-master\SuperMario\mario.h(15,7):
      参见“Mario”的声明
  
D:\SuperMario-master\SuperMario\mario.cpp(257,68): error C2065: “i”: 未声明的标识符
D:\SuperMario-master\SuperMario\mario.cpp(258,25): error C2065: “i”: 未声明的标识符
D:\SuperMario-master\SuperMario\mario.cpp(259,68): error C2065: “i”: 未声明的标识符
D:\SuperMario-master\SuperMario\mario.cpp(260,25): error C2065: “i”: 未声明的标识符
D:\SuperMario-master\SuperMario\mario.cpp(261,66): error C2065: “i”: 未声明的标识符
D:\SuperMario-master\SuperMario\mario.cpp(262,23): error C2065: “i”: 未声明的标识符
D:\SuperMario-master\SuperMario\mario.cpp(268,3): error C3927: "->": 非函数声明符后不允许尾随返回类型
D:\SuperMario-master\SuperMario\mario.cpp(268,14): error C3613: “->”后缺少返回类型(假定为“int”)
D:\SuperMario-master\SuperMario\mario.cpp(268,3): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\SuperMario-master\SuperMario\mario.cpp(268,14): error C2146: 语法错误: 缺少“;”(在标识符“mario”的前面)
D:\SuperMario-master\SuperMario\mario.cpp(269,3): error C2059: 语法错误:“if”
D:\SuperMario-master\SuperMario\mario.cpp(271,2): error C2059: 语法错误:“}”
D:\SuperMario-master\SuperMario\mario.cpp(271,2): error C2143: 语法错误: 缺少“;”(在“}”的前面)
D:\SuperMario-master\SuperMario\mario.cpp(272,7): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\SuperMario-master\SuperMario\mario.cpp(272,7): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\SuperMario-master\SuperMario\mario.cpp(282,2): error C2059: 语法错误:“if”
D:\SuperMario-master\SuperMario\mario.cpp(283,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
D:\SuperMario-master\SuperMario\mario.cpp(283,1): error C2059: 语法错误:“}”
  monster.cpp
D:\SuperMario-master\SuperMario\mario.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“monster.cpp”)
  
  vector.cpp
  正在生成代码...
